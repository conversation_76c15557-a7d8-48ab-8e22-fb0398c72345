package com.holidu.referral.referrer

import com.bookiply.datalake.records.AgentReferralCodeRecord
import com.fasterxml.jackson.annotation.JsonTypeName
import com.holidu.referral.infrastructure.datalake.DatalakeRecord
import com.holidu.referral.infrastructure.datalake.Indexable
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.time.Instant

typealias ReferrerDatalakeRecord = AgentReferralCodeRecord

@Entity
@JsonTypeName("referrer")
data class Referrer(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,

    @Column(nullable = false, unique = true)
    var legalEntityId: Long,

    @Column(nullable = false, unique = true)
    var referralCode: String,

    @Column(nullable = false)
    var marketingEmailsAccepted: <PERSON><PERSON>an,

    @Column(nullable = false)
    var termsAndConditionsAccepted: <PERSON><PERSON><PERSON>,

    @CreationTimestamp
    val createdAt: Instant = Instant.now(),

    @UpdateTimestamp
    val updatedAt: Instant = Instant.now(),
) : Indexable, Serializable {
    override val firehoseStreamName: String
        get() = "firehose_bookiply_data_agent_referral_code"

    override fun toDatalakeRecord(): DatalakeRecord {
        val referrer = this

        return ReferrerDatalakeRecord.newBuilder().apply {
            agentId = referrer.legalEntityId
            referralCode = referrer.referralCode
            referralCampaignType = "REFERRAL_PROGRAM_V1"
            isActive = true
            createdAt = referrer.createdAt
            updatedAt = referrer.updatedAt
            termsAndConditionsAccepted = referrer.termsAndConditionsAccepted
            marketingEmailsAccepted = referrer.marketingEmailsAccepted
        }.build()
    }
}