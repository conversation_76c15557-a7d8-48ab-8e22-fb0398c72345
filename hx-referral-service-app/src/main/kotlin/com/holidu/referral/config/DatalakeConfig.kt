package com.holidu.referral.config

import com.holidu.firehose.DispatcherConfiguration
import com.holidu.firehose.FirehoseDispatcher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@Configuration
@Profile("!test")
class DatalakeConfig {
    @Bean
    fun firehoseDispatcher(): FirehoseDispatcher {
        System.setProperty(
            "software.amazon.awssdk.http.service.impl",
            "software.amazon.awssdk.http.urlconnection.UrlConnectionSdkHttpService"
        )
        val dispatcherConfiguration = DispatcherConfiguration.Companion.DEFAULT
        val dispatcher = FirehoseDispatcher.Companion.getInstance(dispatcherConfiguration)

        return dispatcher
    }
}