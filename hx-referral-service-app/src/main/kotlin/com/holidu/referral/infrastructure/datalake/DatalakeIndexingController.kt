package com.holidu.referral.infrastructure.datalake

import com.holidu.referral.infrastructure.repository.payment.PaymentRepository
import com.holidu.referral.infrastructure.repository.referee.RefereeRepository
import com.holidu.referral.infrastructure.repository.referrer.ReferrerRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/internal/api/v1/datalake/indexing")
class DatalakeIndexingController(
    private val referrerRepository: ReferrerRepository,
    private val refereeRepository: RefereeRepository,
    private val paymentRepository: PaymentRepository,
    private val indexer: Indexer
) {
    @PostMapping("/index-all")
    fun indexAll(@RequestBody indexAllCommand: IndexAllCommand) =
        when (indexAllCommand.typeToIndex) {
            IndexedType.REFERRER -> getAndIndexAllReferrers()
            IndexedType.REFEREE -> getAndIndexAllReferees()
            IndexedType.PAYMENT -> getAndIndexAllPayments()
        }

    private fun getAndIndexAllReferrers() =
        getAndIndexAllEntitiesInChunks(
            loadPage = { pageable -> referrerRepository.findAll(pageable) },
            processPage = { referrers -> indexer.indexBatch(referrers) }
        )

    private fun getAndIndexAllReferees() =
        getAndIndexAllEntitiesInChunks(
            loadPage = { pageable -> refereeRepository.findAll(pageable) },
            processPage = { referees -> indexer.indexBatch(referees) }
        )

    private fun getAndIndexAllPayments() =
        getAndIndexAllEntitiesInChunks(
            loadPage = { pageable -> paymentRepository.findAllIncludingDeleted(pageable) },
            processPage = { payments -> indexer.indexBatch(payments) }
        )

    fun <T> getAndIndexAllEntitiesInChunks(
        loadPage: (Pageable) -> Page<T>,
        processPage: (List<T>) -> Unit
    ) {
        var pageNumber = 0
        var hasMorePages = true

        while (hasMorePages) {
            val pageable = PageRequest.of(pageNumber, BATCH_SIZE_50)
            val page = loadPage(pageable)

            if (page.hasContent()) {
                processPage(page.content)
                pageNumber++
                hasMorePages = page.hasNext()
            } else {
                hasMorePages = false
            }
        }
    }

    companion object {
        private const val BATCH_SIZE_50 = 50
    }

}

data class IndexAllCommand(
    val typeToIndex: IndexedType
)

enum class IndexedType {
    REFERRER,
    REFEREE,
    PAYMENT
}