package com.holidu.referral.infrastructure.repository.referee

import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.infrastructure.datalake.Indexer
import com.holidu.referral.referee.Referee
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component

@Component
class RefereeRepository(
    private val refereeSpringJpaRepository: RefereeSpringJpaRepository,
    private val indexer: Indexer
) {
    fun save(referee: Referee): Referee =
        refereeSpringJpaRepository.save(referee)
            .also {
                indexer.index(it)
            }

    fun findAll(): List<Referee> =
        refereeSpringJpaRepository.findAll()

    fun findAll(pageable: Pageable): Page<Referee> =
        refereeSpringJpaRepository.findAll(pageable)

    fun findByLegalEntityId(legalEntityId: Long): Referee? =
        refereeSpringJpaRepository.findByLegalEntityId(legalEntityId)

    fun findByNameIsNull(): List<Referee> =
        refereeSpringJpaRepository.findByNameIsNull()

    fun findByReferrerId(referrerId: Long): List<Referee> =
        refereeSpringJpaRepository.findByReferrerId(referrerId)

    fun findByLegalEntityIdIn(refereeLegalEntityIds: List<RefereeLegalEntityId>): List<Referee> {
        return refereeSpringJpaRepository.findByLegalEntityIdIn(refereeLegalEntityIds.map { it.value })
    }
}