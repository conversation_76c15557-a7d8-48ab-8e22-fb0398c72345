package com.holidu.referral.infrastructure.repository.payment

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.infrastructure.events.datalake.IndexSingleEntityCommand
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.Payment
import com.holidu.referral.payment.PaymentState
import com.holidu.referral.referee.Referee
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import java.time.Instant

class PaymentRepositoryIntegrationTest @Autowired constructor(
    private val sut: PaymentRepository
) : IntegrationTestBase() {
    @Test
    fun `when payment is saved, a command to index payment is sent`() {
        val referrer = createReferrer(legalEntityId = 128L)
        val referee = Referee(
            legalEntityId = 931L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val property = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            parentListingId = 95L,
            unitTypeId = 95L
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            unitTypeId = property.unitTypeId,
            creationDate = NOW.minusMinutes(7),
            reward = 75,
            linkedEntityType = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.PENDING
        )

        val result = sut.save(payment)

        mockCommandPublisher.sentCommands shouldContain IndexSingleEntityCommand(
            entity = Payment(
                id = result.id,
                referrerId = referrer.id,
                referrerLegalEntityId = 128,
                refereeLegalEntityId = 931,
                unitTypeId = 95,
                creationDate = NOW.minusMinutes(7),
                reward = 75,
                linkedEntityType = LinkedEntityType.REFERRED_PROPERTY,
                state = PaymentState.PENDING,
                createdAt = result.createdAt,
                updatedAt = result.updatedAt
            ),
            streamName = "firehose_bookiply_data_agent_referral_payout"
        )
    }

    @Test
    fun `failure to index payment does NOT block saving payment to database`() {
        val referrer = createReferrer(legalEntityId = 7L)
        val bonus = createBonus(
            referrerId = referrer.id,
            numberOfPayments = 3
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            creationDate = NOW.minusHours(2),
            reward = 150,
            linkedEntityType = LinkedEntityType.BONUS,
            bonusId = bonus.id,
            state = PaymentState.PENDING
        )
        mockFirehoseWriter.throwExceptionOnCalls()

        sut.save(payment)
        val result = sut.findByBonusId(bonus.id)!!

        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe 7L
        result.creationDate shouldBeUpToSeconds NOW.minusHours(2)
        result.bonusId shouldBe bonus.id
        result.reward shouldBe 150
        result.linkedEntityType shouldBe LinkedEntityType.BONUS
        result.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `failure to send command to index referee does NOT block saving referee to database`() {
        val referrer = createReferrer(legalEntityId = 98L)
        val bonus = createBonus(
            referrerId = referrer.id,
            numberOfPayments = 3
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            creationDate = NOW.minusHours(5),
            reward = 300,
            linkedEntityType = LinkedEntityType.BONUS,
            bonusId = bonus.id,
            state = PaymentState.PENDING
        )
        mockCommandPublisher.throwExceptionOnCalls()

        sut.save(payment)
        val result = sut.findByBonusId(bonus.id)!!

        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe 98L
        result.bonusId shouldBe bonus.id
        result.creationDate shouldBeUpToSeconds NOW.minusHours(5)
        result.reward shouldBe 300
        result.linkedEntityType shouldBe LinkedEntityType.BONUS
        result.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `findAllIncludingDeleted gets deleted payments along with regular`() {
        val referrer = createReferrer()
        val bonusOne = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        val bonusTwo = createBonus(referrerId = referrer.id, numberOfPayments = 10)
        val payment = createPayment(
                referrerId = referrer.id,
                referrerLegalEntityId = referrer.legalEntityId,
                creationDate = NOW.minusHours(5),
                reward = 300,
                type = LinkedEntityType.BONUS,
                bonusId = bonusOne.id,
                state = PaymentState.PENDING
            )
        val deletedPayment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            creationDate = NOW.minusHours(5),
            reward = 1000,
            type = LinkedEntityType.BONUS,
            bonusId = bonusTwo.id,
            state = PaymentState.PENDING
        ).also {
            it.deletedAt = Instant.now()
            sut.save(it)
        }

        val result = sut.findAllIncludingDeleted(PageRequest.of(0, 10)).content

        result.size shouldBe 2
        result.map { it.reward } shouldContainExactlyInAnyOrder
                listOf(
                    payment.reward,
                    deletedPayment.reward
                )
    }
}
